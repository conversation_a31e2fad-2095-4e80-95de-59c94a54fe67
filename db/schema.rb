# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_16_123239) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "booking_links", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.string "name", null: false
    t.text "description"
    t.boolean "active", default: true
    t.string "slug", null: false
    t.text "preferred_days", default: [], array: true
    t.integer "duration", default: 60
    t.string "color", default: "#4a86e8"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "location"
    t.boolean "location_required", default: false
    t.boolean "is_remote", default: false
    t.integer "morning_limit"
    t.integer "afternoon_limit"
    t.integer "daily_limit"
    t.boolean "include_works_in_count", default: true
    t.boolean "book_holidays", default: false
    t.index ["company_id", "slug"], name: "index_booking_links_on_company_id_and_slug", unique: true
    t.index ["company_id"], name: "index_booking_links_on_company_id"
  end

  create_table "bookings", force: :cascade do |t|
    t.bigint "booking_link_id", null: false
    t.bigint "company_id", null: false
    t.string "client_name", null: false
    t.string "client_email", null: false
    t.string "client_phone"
    t.date "preferred_date", null: false
    t.string "preferred_period", null: false
    t.text "message"
    t.string "status", default: "pending"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "location"
    t.float "latitude"
    t.float "longitude"
    t.integer "duration"
    t.string "access_token"
    t.datetime "token_generated_at"
    t.datetime "specific_time"
    t.datetime "confirmed_time"
    t.index ["access_token"], name: "index_bookings_on_access_token", unique: true
    t.index ["booking_link_id"], name: "index_bookings_on_booking_link_id"
    t.index ["client_email"], name: "index_bookings_on_client_email"
    t.index ["company_id", "status"], name: "index_bookings_on_company_id_and_status"
    t.index ["company_id"], name: "index_bookings_on_company_id"
  end

  create_table "breaks", force: :cascade do |t|
    t.bigint "daily_log_id", null: false
    t.datetime "start_time", null: false
    t.datetime "end_time"
    t.integer "break_type", default: 0
    t.integer "duration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.bigint "contract_id", null: false
    t.index ["contract_id"], name: "index_breaks_on_contract_id"
    t.index ["daily_log_id"], name: "index_breaks_on_daily_log_id"
    t.index ["user_id", "start_time"], name: "index_breaks_on_user_and_date"
    t.index ["user_id"], name: "index_breaks_on_user_id"
  end

  create_table "companies", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.string "subdomain"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "paid_terms_accepted_at", default: false
    t.boolean "is_personal", default: false
    t.string "web"
    t.string "description"
    t.string "address"
    t.string "phone"
    t.integer "contracts_count", default: 0, null: false
  end

  create_table "company_settings", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.integer "break_duration", default: 30
    t.boolean "auto_break", default: true
    t.boolean "auto_end", default: true
    t.boolean "allow_overtime", default: true
    t.string "timezone", default: "Prague"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "approve_vacations", default: false
    t.boolean "daily_team_reports", default: false
    t.index ["company_id"], name: "index_company_settings_on_company_id"
  end

  create_table "company_user_roles", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "user_id"
    t.bigint "role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_primary"
    t.boolean "active", default: true
    t.string "previous_role_name"
    t.index ["active"], name: "index_company_user_roles_on_active"
    t.index ["company_id", "previous_role_name"], name: "index_company_user_roles_on_company_id_and_previous_role_name"
    t.index ["company_id"], name: "index_company_user_roles_on_company_id"
    t.index ["role_id"], name: "index_company_user_roles_on_role_id"
    t.index ["user_id"], name: "index_company_user_roles_on_user_id"
  end

  create_table "contracts", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "user_id"
    t.string "first_name"
    t.string "last_name"
    t.string "contract_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "invitations_count"
    t.string "job_title"
    t.string "prefix"
    t.string "suffix"
    t.string "work_place"
    t.datetime "valid_since"
    t.datetime "valid_through"
    t.boolean "active", default: true, null: false
    t.string "email"
    t.string "phone"
    t.integer "status", default: 0
    t.index ["company_id", "email"], name: "index_contracts_on_company_id_and_email"
    t.index ["company_id"], name: "index_contracts_on_company_id"
    t.index ["user_id"], name: "index_contracts_on_user_id"
  end

  create_table "daily_activities", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "user_id", null: false
    t.bigint "daily_log_id"
    t.bigint "contract_id"
    t.datetime "start_time"
    t.datetime "end_time"
    t.integer "duration"
    t.text "description"
    t.string "place"
    t.string "project"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "work_id"
    t.bigint "work_assignment_id"
    t.string "activity_type"
    t.string "location_coordinates"
    t.index ["activity_type"], name: "index_daily_activities_on_activity_type"
    t.index ["company_id"], name: "index_daily_activities_on_company_id"
    t.index ["contract_id"], name: "index_daily_activities_on_contract_id"
    t.index ["daily_log_id"], name: "index_daily_activities_on_daily_log_id"
    t.index ["user_id"], name: "index_daily_activities_on_user_id"
    t.index ["work_assignment_id"], name: "index_daily_activities_on_work_assignment_id"
    t.index ["work_id"], name: "index_daily_activities_on_work_id"
  end

  create_table "daily_logs", force: :cascade do |t|
    t.bigint "contract_id", null: false
    t.bigint "company_id", null: false
    t.bigint "user_id"
    t.datetime "start_time"
    t.datetime "end_time"
    t.text "description"
    t.string "place"
    t.string "status"
    t.string "start_ip"
    t.integer "duration"
    t.integer "workday"
    t.float "latitude"
    t.float "longitude"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_daily_logs_on_company_id"
    t.index ["contract_id"], name: "index_daily_logs_on_contract_id"
    t.index ["user_id"], name: "index_daily_logs_on_user_id"
  end

  create_table "events", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "contract_id", null: false
    t.bigint "user_id"
    t.datetime "start_time"
    t.datetime "end_time"
    t.integer "event_type"
    t.string "status"
    t.text "description"
    t.integer "duration"
    t.integer "workday"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "place"
    t.float "latitude"
    t.float "longitude"
    t.string "title"
    t.index ["company_id"], name: "index_events_on_company_id"
    t.index ["contract_id"], name: "index_events_on_contract_id"
    t.index ["user_id"], name: "index_events_on_user_id"
  end

  create_table "feedbacks", force: :cascade do |t|
    t.bigint "company_id"
    t.bigint "user_id"
    t.string "page_url", null: false
    t.integer "category", default: 0, null: false
    t.text "message", null: false
    t.string "user_plan"
    t.string "user_email", null: false
    t.string "user_company_name"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_feedbacks_on_category"
    t.index ["company_id", "created_at"], name: "index_feedbacks_on_company_id_and_created_at"
    t.index ["company_id"], name: "index_feedbacks_on_company_id"
    t.index ["status"], name: "index_feedbacks_on_status"
    t.index ["user_id"], name: "index_feedbacks_on_user_id"
  end

  create_table "holidays", force: :cascade do |t|
    t.date "date", null: false
    t.string "country", null: false
    t.string "month_year", null: false
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["date", "country"], name: "index_holidays_on_date_and_country", unique: true
    t.index ["month_year"], name: "index_holidays_on_month_year"
  end

  create_table "invitations", force: :cascade do |t|
    t.bigint "sender_id", null: false
    t.bigint "recipient_id"
    t.bigint "company_id", null: false
    t.string "email", null: false
    t.string "unique_token", null: false
    t.string "status", default: "pending", null: false
    t.datetime "expires_at", null: false
    t.datetime "accepted_at"
    t.json "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "contract_id"
    t.string "first_name"
    t.string "last_name"
    t.index ["company_id"], name: "index_invitations_on_company_id"
    t.index ["recipient_id"], name: "index_invitations_on_recipient_id"
    t.index ["sender_id", "email"], name: "index_invitations_on_sender_id_and_email", unique: true
    t.index ["sender_id"], name: "index_invitations_on_sender_id"
    t.index ["status", "expires_at"], name: "index_invitations_on_status_and_expires_at"
    t.index ["unique_token"], name: "index_invitations_on_unique_token", unique: true
  end

  create_table "jwt_denylist", force: :cascade do |t|
    t.string "jti", null: false
    t.datetime "exp", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["jti"], name: "index_jwt_denylist_on_jti"
  end

  create_table "meeting_users", force: :cascade do |t|
    t.bigint "meeting_id", null: false
    t.bigint "contract_id"
    t.string "email", null: false
    t.string "token", null: false
    t.jsonb "selected_dates", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.integer "status", default: 0
    t.index ["contract_id"], name: "index_meeting_users_on_contract_id"
    t.index ["meeting_id", "email"], name: "index_meeting_users_on_meeting_id_and_email", unique: true
    t.index ["meeting_id"], name: "index_meeting_users_on_meeting_id"
    t.index ["token"], name: "index_meeting_users_on_token", unique: true
    t.index ["user_id"], name: "index_meeting_users_on_user_id"
  end

  create_table "meetings", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "created_by_id", null: false
    t.string "title", null: false
    t.text "description"
    t.string "place"
    t.jsonb "day_options", default: {}, null: false
    t.datetime "confirmed_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "status", default: 0
    t.index ["company_id", "created_at"], name: "index_meetings_on_company_id_and_created_at"
    t.index ["company_id"], name: "index_meetings_on_company_id"
    t.index ["created_by_id"], name: "index_meetings_on_created_by_id"
  end

  create_table "notifications", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "company_id", null: false
    t.string "notifiable_type", null: false
    t.bigint "notifiable_id", null: false
    t.string "notification_type"
    t.string "title"
    t.text "message"
    t.json "data"
    t.datetime "read_at"
    t.datetime "processed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id", "created_at"], name: "index_notifications_on_company_id_and_created_at"
    t.index ["company_id"], name: "index_notifications_on_company_id"
    t.index ["created_at"], name: "index_notifications_on_created_at"
    t.index ["notifiable_type", "notifiable_id"], name: "index_notifications_on_notifiable"
    t.index ["notification_type"], name: "index_notifications_on_notification_type"
    t.index ["user_id", "read_at"], name: "index_notifications_on_user_id_and_read_at"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "orders", force: :cascade do |t|
    t.string "client_name"
    t.string "client_email"
    t.string "client_phone"
    t.text "service_requested"
    t.string "status", default: "pending"
    t.text "notes"
    t.bigint "company_id", null: false
    t.bigint "work_id"
    t.datetime "requested_date"
    t.string "location"
    t.float "latitude"
    t.float "longitude"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["client_email"], name: "index_orders_on_client_email"
    t.index ["company_id", "status"], name: "index_orders_on_company_id_and_status"
    t.index ["company_id"], name: "index_orders_on_company_id"
    t.index ["work_id"], name: "index_orders_on_work_id"
  end

  create_table "plans", force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.string "price", default: "0,00", null: false
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "duration", default: 30
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "service_contracts", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.string "status", default: "scheduled"
    t.bigint "client_id"
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id"], name: "index_service_contracts_on_client_id"
    t.index ["company_id"], name: "index_service_contracts_on_company_id"
  end

  create_table "settings", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.string "day_start"
    t.string "day_end"
    t.string "lunch_start"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_settings_on_company_id"
  end

  create_table "subscriptions", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "plan_id", null: false
    t.date "start_date", null: false
    t.date "expire_date", null: false
    t.string "status", default: "active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_trial", default: false
    t.bigint "trial_activated_by_id"
    t.index ["company_id"], name: "index_subscriptions_on_company_id"
    t.index ["plan_id"], name: "index_subscriptions_on_plan_id"
    t.index ["trial_activated_by_id"], name: "index_subscriptions_on_trial_activated_by_id"
  end

  create_table "user_profiles", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "prefix"
    t.string "title_prefix"
    t.string "title_suffix"
    t.string "location"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_profiles_on_user_id"
  end

  create_table "user_settings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.time "start_time"
    t.time "end_time"
    t.time "break_start"
    t.boolean "auto_break", default: true
    t.string "timezone", default: "Prague"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "break_duration", default: 30
    t.string "country_code"
    t.string "language"
    t.string "language_code", default: "cs"
    t.index ["user_id"], name: "index_user_settings_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "force_password_change", default: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.integer "sign_in_count"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.string "invitation_token"
    t.datetime "invitation_created_at"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.boolean "tos_accepted_at", default: false
    t.boolean "gdpr_accepted_at", default: false
    t.datetime "tokens_valid_after"
    t.string "jti"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by"
    t.index ["jti"], name: "index_users_on_jti", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["tokens_valid_after"], name: "index_users_on_tokens_valid_after"
  end

  create_table "work_assignments", force: :cascade do |t|
    t.bigint "work_id", null: false
    t.bigint "contract_id", null: false
    t.bigint "company_id", null: false
    t.string "role", default: "worker"
    t.boolean "is_lead", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id", "contract_id"], name: "index_work_assignments_on_company_id_and_contract_id"
    t.index ["company_id"], name: "index_work_assignments_on_company_id"
    t.index ["contract_id"], name: "index_work_assignments_on_contract_id"
    t.index ["work_id", "contract_id"], name: "index_work_assignments_on_work_id_and_contract_id", unique: true
    t.index ["work_id"], name: "index_work_assignments_on_work_id"
  end

  create_table "work_sessions", force: :cascade do |t|
    t.bigint "work_id", null: false
    t.bigint "user_id", null: false
    t.bigint "company_id", null: false
    t.datetime "start_time", null: false
    t.datetime "end_time"
    t.integer "duration"
    t.text "notes"
    t.string "status", default: "in_progress"
    t.float "latitude"
    t.float "longitude"
    t.string "ip_address"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "check_in_time"
    t.datetime "check_out_time"
    t.string "check_in_location"
    t.bigint "daily_activity_id"
    t.bigint "work_assignment_id"
    t.string "check_in_notes"
    t.index ["company_id"], name: "index_work_sessions_on_company_id"
    t.index ["daily_activity_id"], name: "index_work_sessions_on_daily_activity_id"
    t.index ["user_id", "start_time"], name: "index_work_sessions_on_user_id_and_start_time"
    t.index ["user_id"], name: "index_work_sessions_on_user_id"
    t.index ["work_assignment_id"], name: "index_work_sessions_on_work_assignment_id"
    t.index ["work_id", "status"], name: "index_work_sessions_on_work_id_and_status"
    t.index ["work_id"], name: "index_work_sessions_on_work_id"
  end

  create_table "works", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.string "location"
    t.string "status", default: "scheduled"
    t.date "scheduled_start_date"
    t.date "scheduled_end_date"
    t.bigint "client_id"
    t.bigint "company_id", null: false
    t.string "work_type"
    t.boolean "is_recurring", default: false
    t.float "latitude"
    t.float "longitude"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "booking_id"
    t.integer "duration"
    t.string "preferred_period"
    t.datetime "specific_time"
    t.datetime "confirmed_time"
    t.bigint "service_contract_id"
    t.index ["booking_id"], name: "index_works_on_booking_id"
    t.index ["client_id"], name: "index_works_on_client_id"
    t.index ["company_id", "status"], name: "index_works_on_company_id_and_status"
    t.index ["company_id"], name: "index_works_on_company_id"
    t.index ["service_contract_id"], name: "index_works_on_service_contract_id"
    t.index ["status", "scheduled_start_date"], name: "index_works_on_status_and_scheduled_start_date"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "booking_links", "companies"
  add_foreign_key "bookings", "booking_links"
  add_foreign_key "bookings", "companies"
  add_foreign_key "breaks", "contracts"
  add_foreign_key "breaks", "daily_logs"
  add_foreign_key "breaks", "users"
  add_foreign_key "company_settings", "companies", on_delete: :cascade
  add_foreign_key "company_user_roles", "companies"
  add_foreign_key "company_user_roles", "roles"
  add_foreign_key "company_user_roles", "users", on_delete: :nullify
  add_foreign_key "contracts", "companies"
  add_foreign_key "contracts", "users", on_delete: :nullify
  add_foreign_key "daily_activities", "companies"
  add_foreign_key "daily_activities", "contracts"
  add_foreign_key "daily_activities", "daily_logs"
  add_foreign_key "daily_activities", "users"
  add_foreign_key "daily_activities", "work_assignments"
  add_foreign_key "daily_activities", "works"
  add_foreign_key "daily_logs", "companies"
  add_foreign_key "daily_logs", "contracts"
  add_foreign_key "daily_logs", "users", on_delete: :nullify
  add_foreign_key "events", "companies"
  add_foreign_key "events", "contracts"
  add_foreign_key "events", "users"
  add_foreign_key "feedbacks", "companies"
  add_foreign_key "feedbacks", "users"
  add_foreign_key "invitations", "companies", on_delete: :cascade
  add_foreign_key "invitations", "contracts"
  add_foreign_key "invitations", "users", column: "recipient_id", on_delete: :nullify
  add_foreign_key "invitations", "users", column: "sender_id", on_delete: :nullify
  add_foreign_key "meeting_users", "contracts"
  add_foreign_key "meeting_users", "meetings"
  add_foreign_key "meeting_users", "users"
  add_foreign_key "meetings", "companies"
  add_foreign_key "meetings", "users", column: "created_by_id"
  add_foreign_key "notifications", "companies"
  add_foreign_key "notifications", "users"
  add_foreign_key "orders", "companies"
  add_foreign_key "orders", "works", on_delete: :nullify
  add_foreign_key "settings", "companies", on_delete: :cascade
  add_foreign_key "subscriptions", "companies"
  add_foreign_key "subscriptions", "plans"
  add_foreign_key "subscriptions", "users", column: "trial_activated_by_id"
  add_foreign_key "user_profiles", "users", on_delete: :cascade
  add_foreign_key "user_settings", "users", on_delete: :cascade
  add_foreign_key "work_assignments", "companies"
  add_foreign_key "work_assignments", "contracts"
  add_foreign_key "work_assignments", "works"
  add_foreign_key "work_sessions", "companies"
  add_foreign_key "work_sessions", "daily_activities"
  add_foreign_key "work_sessions", "users"
  add_foreign_key "work_sessions", "work_assignments"
  add_foreign_key "work_sessions", "works"
  add_foreign_key "works", "bookings"
  add_foreign_key "works", "companies"
  add_foreign_key "works", "service_contracts"
end
