# Endless Redirect/Refresh Loop Fix

## Problem Description
When server restarts and breaks existing JWT sessions, users get stuck in an infinite redirect loop:
- <PERSON><PERSON><PERSON> requests `/cs/users/sign_in` 
- <PERSON><PERSON> requires authentication for the login page itself
- Redirects to... the same login page
- Creates infinite loop

## Root Causes Identified

### Layer 1: Accept Header Issue
- Requests came with `Accept: */*` instead of `text/html`
- SpaController's `skip_before_action` only worked for HTML format
- Authentication was required even for login pages

### Layer 2: Missing Public Pages
- `public_spa_page?` method didn't include login/sign_in routes
- Login pages were treated as protected routes

### Layer 3: Redirect Logic
- `require_login` redirected to `spa_login_override_path` 
- This path pointed to the same URL causing the loop

## Implemented Fixes

### 1. SpaController (app/controllers/spa_controller.rb)
- Changed skip_before_action to ALWAYS skip auth for SPA pages
- SPA shell delivery is now always public
- Authentication happens client-side via Vue router

### 2. ApplicationController (app/controllers/application_controller.rb)
#### Updated public_spa_page? method:
- Added all auth-related paths as public
- Login, registration, password reset, email confirmation routes
- Handles paths with and without locale prefixes

#### Enhanced require_login method:
- Added comprehensive debug logging
- For SPA controller: returns JSON 401 instead of redirect
- Checks actual Accept header, not just Rails format
- Prevents redirect loops by returning JSON for SPA requests

### 3. Debug Logging
Added `[AUTH DEBUG]` logging throughout to trace:
- Request format and Accept headers
- Controller and action names
- Authentication flow decisions
- Public vs protected page detection

## Testing Instructions

1. **Restart the server** to apply changes
2. **Test the fix:**
   - Clear browser cache/cookies
   - Navigate to any protected page
   - Should redirect to login without loop
   - Login page should load without authentication

3. **Monitor logs for:**
   - `[AUTH DEBUG]` messages showing flow
   - No repeated redirects to same URL
   - Proper JSON responses for SPA pages

## Expected Behavior

- Login pages are always accessible without authentication
- Expired sessions redirect to login ONCE
- Vue router handles client-side navigation
- No user action required to recover from broken sessions
- Works with any Accept header format

## Critical Success Criteria

✅ NO cache clearing required by users
✅ NO manual intervention needed
✅ Graceful recovery from server restarts
✅ Works with all browser Accept headers
✅ Client-side auth handling via Vue/axios