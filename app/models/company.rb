class Company < ApplicationRecord

  has_many :company_user_roles, dependent: :destroy
  has_many :users, through: :company_user_roles
  has_many :contracts
  has_many :subscriptions, dependent: :destroy
  has_many :plans, through: :subscriptions
  has_many :daily_logs, through: :contracts, dependent: :destroy
  has_many :events, through: :contracts, dependent: :destroy
  has_one :company_setting, dependent: :destroy
  has_many :breaks, through: :contracts, dependent: :destroy
  has_many :works, dependent: :destroy
  has_many :service_contracts, dependent: :destroy
  has_many :work_assignments, dependent: :destroy
  has_many :work_sessions, dependent: :destroy
  has_many :orders, dependent: :destroy
  has_many :bookings, dependent: :destroy
  has_many :booking_links, dependent: :destroy
  has_many :meetings, dependent: :destroy
  has_one_attached :logo
  has_one_attached :logo_thumbnail
  
  # FIXME: nullify contract on company deletion: in model and add o schema

  #validate :name, presence: true
  validates :name, presence: true, uniqueness: { message: :taken }
  validates_uniqueness_of :subdomain, :allow_blank => true, :message => :taken
  
  after_create :create_default_settings
  # after_commit :generate_logo_thumbnail, if: -> { logo.attached? && saved_change_to_logo_attachment? }
  # Usually after_update/create is enough. Yet <PERSON><PERSON> stops any SQL after first query stops. 
  after_commit :create_slug, if: :saved_change_to_name?, on: [:create, :update] 

  # TODO: Add baseagreement 
  # TODO: Add main_id to company
  
  #Authorization with ActionPolicy rules
  #If the user has the right to manage the company
  def users_with_role(role_name)
    users.joins(:company_user_roles, :roles).where(roles: {name: role_name})
  end

  def current_subscription
    # subscriptions.active.order(expire_date: :desc).first
    @current_subscription ||= subscriptions.active.order(expire_date: :desc).first
  end

  def current_plan
    # current_subscription&.plan
    @current_plan ||= current_subscription&.plan
  end

  # Generates a URL for the logo thumbnail variant.
  # This URL might be a direct S3 link or a proxied URL depending on the storage service.
  # For S3 with public: false, it will be a signed URL.
  def logo_url
    return unless logo.attached?
    begin
      logo_thumbnail_url
    rescue => e
      Rails.logger.error("Error generating thumbnail: #{e.message}")
      original_logo_url
    end
  end

  # Generates a URL for the original logo.
  # Similar to logo_url, handles different storage services and generates signed URLs if necessary.
  def logo_thumbnail_url
    return unless logo.attached?
    
    # Set default URL options for this request
    default_url_options = Rails.application.config.active_storage.url_options
    Rails.application.routes.default_url_options = default_url_options
    
    # Generate the URL
    Rails.application.routes.url_helpers.rails_blob_url(
      logo.variant(resize_to_limit: [200, 200]).processed
    )
  rescue => e
    Rails.logger.error("Error generating thumbnail: #{e.message}")
    nil
  end

  def original_logo_url
    return unless logo.attached?
    logo.url
  end

  def available_roles
    if current_plan&.name == 'premium'
      Role.all 
    elsif current_plan&.name == 'plus'
      Role.where(name: ['owner', 'employee', 'admin', 'supervisor'])
    else
      # Free tier: owner only if no existing owner
      if users_with_role('owner').exists?
        Role.where(name: ['employee'])
      else
        Role.where(name: ['owner', 'employee'])
      end
    end
  end

  def self.model_t(key, **options)
    I18n.t("models.company.#{key}", **options)
  end

  private

  def create_default_settings
    build_company_setting.save
  end
  
  #Create a slug from the company name
  def create_slug
    if @attempts.blank?
        update_column :slug, self.name.sub(/,*\s(\w+[.])+.*\z/,'').parameterize
        update_column :subdomain, self.slug
    else
        update_column :slug, "#{self.name.sub(/,*\s(\w+[.])+.*\z/,'').parameterize}-#{@attempts}"
        update_column :subdomain, self.slug
    end
  rescue ActiveRecord::RecordNotUnique => e
    @attempts = @attempts.to_i + 1
    retry if @attempts < 5
    raise e, "Retries exhausted"
  end

  def saved_change_to_logo_attachment?
    attachment_changes.key?('logo')
  end

  # def generate_logo_thumbnail
  #   return unless logo.attached?
    
  #   # Detach old thumbnail if exists
  #   logo_thumbnail.detach if logo_thumbnail.attached?
    
  #   # Use Active Storage's built-in variant method to process the image
  #   thumbnail = logo.variant(resize_to_fill: [100, 100]).processed
    
  #   # Attach the processed variant as the thumbnail
  #   logo_thumbnail.attach(
  #     io: URI.open(thumbnail.url), 
  #     filename: "thumbnail-#{logo.filename}",
  #     content_type: logo.content_type
  #   )
  # end
end
