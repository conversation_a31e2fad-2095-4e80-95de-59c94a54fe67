class ContractsController < ApplicationController
  
  include HolidayFetcher
  include ActionPolicy::Controller

  ### Contract model scoped to ActsAsTenant: company 
  
  #set_current_tenant_through_filter
  before_action :set_tenant_company
  # Authentication handled by ApplicationController's require_login
  before_action :set_contract, except: [:index, :fetch, :new, :create, :colleagues]

  # Exposing IDs in URLs:
  # URLs like /contracts/123 expose company IDs (or slugs). While this isn't inherently a problem, malicious users could attempt brute-force enumeration.
  # Mitigation:
  # Use UUIDs or slugs instead of sequential IDs. UUIDs are much harder to guess.
  # For slugs, make them unique and non-obvious (e.g., acme-co-12345 rather than acme).
  
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def index
    authorize! @company, to: :manage_contract?
  end
  
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def fetch
    if @company.nil?
      redirect_to root_path, alert: contracts_t('errors.no_workspace')
    else
      authorize! @company, to: :manage_contract?

      @contracts = @company.contracts.order(:last_name)
      
      if @contracts.empty?
        redirect_to root_path, alert: contracts_t('errors.no_colleagues')
        return
      end

      current_month = Date.current
      # current_month = Date.new(2025, 1, 31)
      current_month_range = Date.current.beginning_of_month..Date.current.end_of_month
      # current_month_range = Date.new(2025, 1, 1)..Date.new(2025, 1, 31)
      
      holidays = fetch_holidays(current_month.year, current_month.month)
      holiday_dates = holidays.map { |date_str| Date.parse(date_str) }
      
      contract_ids = @contracts.pluck(:id)

      summary_data = DailyLog.where(contract_id: contract_ids, start_time: current_month_range)
                        .group(:contract_id)
                        .select(
                          "contract_id, 
                           COUNT(DISTINCT daily_logs.id) as days_worked, 
                           COALESCE(SUM(duration), 0) as total_duration, 
                           MAX(start_time) as last_active"
                        )
                        .index_by(&:contract_id)
      # End of ident

      events = Event.where(contract_id: contract_ids)
                    .where('(start_time BETWEEN ? AND ?) OR (end_time BETWEEN ? AND ?) OR (start_time <= ? AND end_time >= ?)',
                      current_month_range.begin, current_month_range.end,
                      current_month_range.begin, current_month_range.end,
                      current_month_range.begin, current_month_range.end)

      events_by_contract = {}

      events.each do |event|
        start_date = [event.start_time.to_date, current_month_range.begin].max
        end_date = [event.end_time.to_date, current_month_range.end].min
        
        working_days = (start_date..end_date).count do |date| 
          (1..5).include?(date.wday) && !holiday_dates.include?(date)
        end
        
        events_by_contract[event.contract_id] ||= {}
        events_by_contract[event.contract_id][event.event_type] ||= 0
        events_by_contract[event.contract_id][event.event_type] += working_days
      end
  
      contracts_with_summary = @contracts.map do |contract|
        summary = summary_data[contract.id]
        
        contract.as_json.merge(
          days_worked: summary&.days_worked || 0,
          total_hours: (summary&.total_duration || 0) / 3600.0,
          last_active: summary&.last_active,
          company_name: @company.name,
          event_counts: events_by_contract[contract.id] || {}
        )
      end
      render json: {
        contracts: contracts_with_summary,
        current_user: current_user.id,
        user_role: current_user.role_in(@company)&.name
        }, status: :ok
    end
  end

  # Returns a list of active contracts (colleagues) including or excluding the current user,
  # intended for selection in forms (e.g., assigning work, inviting to meetings).
  # Accessible by any authenticated user within the company.
  def colleagues
    # For now, assume any logged-in user in the company can view colleagues
    # A specific policy like `view_colleagues?` could be added later if needed.
    authorize! @company, to: :view_colleagues?
    
    # Find the current user's contract ID within this company
    current_user_contract_id = @company.contracts.find_by(user_id: current_user.id)&.id

    # Check if we should include the current user (for work assignments)
    include_self = params[:include_self] == 'true'
    
    @colleagues = @company.contracts
                          .where(status: :active)
                          .where.not(user_id: nil)
    
    # Exclude current user unless include_self is true
    @colleagues = @colleagues.where.not(id: current_user_contract_id) unless include_self
    
    @colleagues = @colleagues.select(:id, :first_name, :last_name, :email, :job_title)
                            .order(:last_name, :first_name)

    # Explicitly render only the selected attributes to avoid calling default as_json
    colleagues_data = @colleagues.map { |c| 
      { 
        id: c.id, 
        first_name: c.first_name, 
        last_name: c.last_name, 
        email: c.email, 
        job_title: c.job_title,
        is_current_user: c.id == current_user_contract_id
      } 
    }
    
    render json: {
      colleagues: colleagues_data,
      current_user_contract_id: current_user_contract_id
    }, status: :ok
  end

  # TODO: Remove html format sooner or later
  # If the user is the company owner OR (user has advanced role
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # OR user is contract.user
  def show
    @contract = Contract.find(params[:id])
    authorize! @contract, to: :show?

    @user = User.where(email: @contract.email).first
    
    respond_to do |format|
      format.html
      # format.json { render json: @contract.as_json(methods: [:invitation_sent_at, :invitation_accepted]) }
      format.json { 
        current_role = @contract.user&.role_in(@company)&.name
        available_roles = @company.available_roles.pluck(:name)
        
        render json: @contract.as_json(
          methods: [:invitation_sent_at, :invitation_accepted]
        ).merge({
          current_role: current_role,
          available_roles: available_roles
        })
      }
    end
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def new
    authorize! @company, to: :manage_contract?
    @contract = Contract.new
    render json: { contract: @contract }
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def create
    authorize! @company, to: :manage_contract?

    @contract = current_company.contracts.build(contract_params)

    existing_contract = @company.contracts.find_by(user_id: current_user.id)
    puts "\n ---------- Existing contract: #{existing_contract.inspect} \n\n "
    # no email and existing contract
      # owner has contract ERROR
    # if email and frequent issues (invite self, duplicate contract)
      # ERROR
    # if no error
      # save contract (if no email, model will handle that)

    # If no email, check if only one contract for the owner
    if !@contract.email.present?
      existing_owner_contract = @company.contracts.find_by(user_id: current_user.id)
      if existing_owner_contract
        @contract.errors.add(:base, contracts_t('errors.existing_contract'))
        render json: { success: false, errors: @contract.errors.full_messages }, status: :unprocessable_entity
        return
      else
        @contract.user = current_user 
      end
    #Check for possible invitation errors (invite self, contract duplicate)
    elsif frequent_invitation_issues?(@contract.email, @company)
      render json: { success: false, errors: @contract.errors.full_messages }, status: :unprocessable_entity
      return
    end
    
    # If no previous errors, save new contract (if no email, model will handle that)
    if @contract.save
      render json: { success: true, contract: @contract, message: create_success_message(@contract) }, status: :created
    else
      render json: { success: false, errors: @contract.errors.full_messages }, status: :unprocessable_entity
    end

  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def edit
    @contract = Contract.find(params[:id])
    authorize! @contract, to: :edit?
    
    respond_to do |format|
      format.html
      format.json { render json: @contract }
    end
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def update
    authorize! @contract, to: :edit?
    if @contract.update(contract_params)
      render json: { success: true, contract: @contract, message: contracts_t('messages.updated') }, status: :ok
    else
      render json: { success: false, errors: @contract.errors.full_messages}, status: :unprocessable_entity
    end
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not suspending own contract
  def suspend
    @contract = Contract.find(params[:id])
    
    begin
      authorize! @contract, to: :suspend?
      
      if @contract.suspend!
        render json: { success: true, message: contracts_t('messages.suspended') }, status: :ok
      else
        errors = @contract.errors.full_messages.presence || [contracts_t('errors.suspend_failed')]
        render json: { success: false, errors: errors }, status: :unprocessable_entity
      end
    rescue ActionPolicy::Unauthorized
      render json: { 
        success: false, 
        message: contracts_t('errors.cannot_suspend'), 
        messageType: "error" 
      }, status: :forbidden
    end
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not reactivating own contract
  def reactivate
    @contract = Contract.find(params[:id])
    begin
      authorize! @contract, to: :reactivate?
      
      if @contract.reactivate!
        render json: { success: true, message: contracts_t('messages.reactivated') }, status: :ok
      else
        render json: { success: false, errors: [contracts_t('errors.reactivate_failed')] }, status: :unprocessable_entity
      end
    rescue ActionPolicy::Unauthorized
      render json: { 
        success: false, 
        message: contracts_t('errors.cannot_reactivate'), 
        messageType: "error" 
      }, status: :forbidden
    end
  end
  
  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not terminating own contract
  def terminate
    @contract = Contract.find(params[:id])
    begin
      authorize! @contract, to: :terminate?
      
      if @contract.terminate!
        render json: { success: true, message: contracts_t('messages.terminated') }, status: :ok
      else
        errors = @contract.errors.full_messages.presence || [contracts_t('errors.terminate_failed')]
        render json: { success: false, errors: errors }, status: :unprocessable_entity
      end
    rescue ActionPolicy::Unauthorized
      render json: { 
        success: false, 
        message: contracts_t('errors.cannot_terminate'), 
        messageType: "error" 
      }, status: :forbidden
    end
  end

  # TODO: Check if properly handled rescue
  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # AND the user is not destroying own contract
  def destroy
    begin
      authorize! @contract, to: :destroy?
      
      user = @contract.user
      company_user_role = CompanyUserRole.find_by(user: user, company: @company)
      # If the user is the owner of the company, don't delete his owner role
      if company_user_role&.role&.name == 'owner'
        if @contract.destroy
          render json: { success: true, message: contracts_t('messages.deleted') }, status: :ok
        else
          render json: { success: false, errors: @contract.errors.full_messages }, status: :unprocessable_entity
        end
      else
        company_user_role&.destroy
        if @contract.destroy
          render json: { success: true, message: contracts_t('messages.colleague_deleted') }, status: :ok
        else
          render json: { success: false, errors: @contract.errors.full_messages }, status: :unprocessable_entity
        end
      end
    rescue ActionPolicy::Unauthorized
      render json: { 
        success: false, 
        message: contracts_t('errors.cannot_delete'), 
        messageType: "error" 
      }, status: :forbidden
    end
  end

  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  def resend_invitation
    authorize! @company, to: :manage_contract?
    @contract = current_company.contracts.find(params[:id])

    user = User.find_by(email: @contract.email)

    if user.nil?
      render json: { 
        success: false, 
        errors: [contracts_t('errors.user_not_found')] 
      }, status: :unprocessable_entity
      return
    end

    if user.invitation_accepted_at.present?
      render json: { 
        success: false, 
        errors: [contracts_t('errors.invitation_already_accepted')] 
      }, status: :unprocessable_entity
      return
    end

    begin
    user.invite!  
      render json: { 
        success: true, 
        contract: @contract, 
        message: contracts_t('messages.invitation_resent'),
        should_close_modal: true
      }, status: :ok
    rescue => e
      render json: { 
        success: false, 
        errors: [contracts_t('errors.invitation_resend_failed')] 
        }, status: :unprocessable_entity
    end
    
  end

  # TODO: Probably can be deleted as a new method replaced it
  # If the user is the company owner to manage the company OR (has advanced role 
  # AND the company has a plus or premium plan so the advanced roles are enabled)
  # # AND user is not changing his own role
  # def assign_role_to_user(user, role_name)
  #   authorize! @company, to: :manage_contract?

  #   unless allowed_to?(:can_assign_role?, @company, with: {role_name: role_name})
  #     role_name = 'employee'
  #   end
    
  #   role = Role.find_by(name: role_name)
    
  #   company_user_role = CompanyUserRole.find_or_initialize_by(
  #     user: user,
  #     company: @company
  #   )
  #   company_user_role.role = role
  #   company_user_role.save
  # end


  def update_role
    authorize! @company, to: :manage_contract?
    
    role_name = params[:role_name]
    
    # Free tier owner restriction
    if role_name == 'owner' && @company.current_plan.nil?
      if @company.users_with_role('owner').exists? && !@contract.user&.has_role?('owner', @company)
        return render json: { 
          success: false, 
          message: contracts_t('errors.free_tier_single_owner'),
          upgrade_required: true
        }, status: :forbidden
      end
    end
    
    # Allow re-affirming the owner role for an existing owner on a free tier,
    # as available_roles would otherwise exclude it to prevent adding NEW owners
    is_existing_owner_update = (role_name == 'owner' && @contract.user&.has_role?('owner', @company))
    
    # Check if this role is available for this company plan
    unless @company.available_roles.pluck(:name).include?(role_name) || is_existing_owner_update
      return render json: { success: false, message: contracts_t('errors.role_not_available') }, status: :unprocessable_entity
    end
    
    if @contract.user.nil?
      return render json: { success: false, message: contracts_t('errors.user_not_connected') }, status: :unprocessable_entity
    end
    
    # Safeguard: Ensure company keeps at least one owner
    if role_name != 'owner' && @contract.user&.has_role?('owner', @company)
      if @company.users_with_role('owner').count <= 1
        return render json: { 
          success: false, 
          message: contracts_t('errors.cannot_remove_last_owner') 
        }, status: :forbidden
      end
    end

    # CRITICAL SECURITY FIX: Prevent owner from changing own role
    if current_user.id == @contract.user_id && current_user.has_role?('owner', @company)
      return render json: { 
        success: false, 
        message: contracts_t('errors.cannot_change_own_owner_role') 
      }, status: :forbidden
    end
    
    role = Role.find_by(name: role_name)
    
    company_user_role = CompanyUserRole.find_or_initialize_by(
      user: @contract.user,
      company: @company
    )
    
    company_user_role.role = role
    
    if company_user_role.save
      render json: { success: true, message: contracts_t('messages.role_updated'), role: role.name }
    else
      render json: { success: false, errors: company_user_role.errors.full_messages }, status: :unprocessable_entity
    end
  end


  private
  
  # New invite model, rework to @company be used
  def current_company
    ActsAsTenant.current_tenant
  end

  def set_tenant_company
    @company = ActsAsTenant.current_tenant
  end

  def set_contract
    @contract = @company.contracts.find(params[:id])
  end

  def frequent_invitation_issues?(email, company)
    # Only one contract invitation for email and company
    existing_contracts_invitation = @company.contracts.where(email: email)
                                         .where.not(status: :terminated)
                                         .exists?
    if existing_contracts_invitation
      @contract.errors.add(:base, contracts_t('errors.invitation_exists'))
      return true
    end
    # Owner can't invite self 
    if email == current_user.email
      @contract.errors.add(:base, contracts_t('errors.cannot_invite_self'))
      return true
    end

    false 
  end

  def create_success_message(contract)
    if contract.email.present?
      contracts_t('messages.created_with_invitation', email: contract.email)
    else
      contracts_t('messages.created')
    end
  end

  # Helper method for controller-specific translations
  # Makes translation calls cleaner and more maintainable
  def contracts_t(key, **options)
    t("controllers.contracts.#{key}", **options)
  end
    
  def contract_params
    params.require(:contract).permit(
  :email, 
  :first_name, 
  :last_name, 
  :contract_type, 
  :phone,
  :user_id, 
  :job_title, 
  :prefix, 
  :suffix
)
  end

  def invitation_params
    params.require(:contract).permit(:email)
  end 


end
