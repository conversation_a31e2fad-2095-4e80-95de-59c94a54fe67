class SpaController < ApplicationController
  layout 'spa'
  # CRITICAL FIX: Skip authentication for ALL SPA page requests, not just HTML format
  # The SPA shell must always be delivered without server-side authentication
  # Client-side Vue router and axios interceptors handle authentication
  skip_before_action :require_login, if: -> { 
    # Accept any format for SPA pages - browsers may send */* or text/html
    # The SPA controller only serves the Vue app shell, actual auth happens client-side
    Rails.logger.debug "[AUTH DEBUG] SpaController: Format=#{request.format}, Path=#{request.path}, Skipping auth"
    true 
  }
  
  def index
    # Handle JSON requests appropriately (likely API calls that got routed here)
    respond_to do |format|
      format.html { 
        # Render the SPA shell
        # The Vue app will handle client-side routing
        render 'index' 
      }
      format.json { 
        # For JSON requests routed to SPA controller, return user context
        if current_user
          render json: {
            authenticated: true,
            user: {
              id: current_user.id,
              email: current_user.email
            },
            current_company: ActsAsTenant.current_tenant&.id,
            # TODO: Consider harmonizing this key with `spa_controller: true` used in this
            # controller's require_login rescue block if they serve a similar diagnostic purpose.
            # E.g., use a consistent key like `spa_context: true`.
            spa_route: true
          }
        else
          render json: { 
            authenticated: false, 
            error: 'Authentication required',
            spa_route: true 
          }, status: :unauthorized
        end
      }
    end
  end
  
  private
  
  
  # CHUNK 47: JWT-only mode - Devise sign-in is handled via API
  # def after_sign_in_path_for(resource)
  #   stored_location_for(resource) || spa_dashboard_path(locale: I18n.locale)
  # end
  
end