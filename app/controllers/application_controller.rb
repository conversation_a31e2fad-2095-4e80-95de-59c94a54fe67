class ApplicationController < ActionController::Base
  include LocaleHandler
  include JwtAuthenticatable

  layout :determine_layout
  before_action :http_basic_authenticate
  
  before_action :require_login # FIXME: URGENT:  Only logged in users allowed to use the app. Split landingpages.
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_current_tenant
  before_action :set_locale

  skip_before_action :require_login, if: :devise_controller?

  rescue_from ActionPolicy::Unauthorized, with: :user_not_authorized
  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

  # TODO: First create a non user landing page to redirect to when not logged in and set it as a root path
  # TODO: Test the usuall ruby on rails app security breaks vectors

  # CHUNK 48: JWT-only mode - CSRF protection disabled
  # In JWT-only authentication, CSRF tokens are not needed as:
  # 1. JWTs are sent in Authorization headers (not susceptible to CSRF)
  # 2. No session cookies to hijack
  # 3. Same-origin policy protects against unauthorized API calls
  # protect_from_forgery with: :exception, unless: :devise_controller?
  skip_forgery_protection

  # CHUNK 46: The `current_user` method is now JWT-only.
  # The `|| super` fallback to <PERSON><PERSON>'s session-based user has been removed to
  # enforce a strict JWT-only context and prevent ambiguous authentication states.
  # Any legacy flows (e.g., Devise password reset) must now be handled
  # without relying on a session-based `current_user`.
  def current_user
    @current_user ||= @current_jwt_user
  end

  private

  def determine_layout
    puts "------------------------- Current controller: #{params[:controller]} ------------------------" # TODO: Remove debug puts statement
    if current_user.present?
      # Use SPA layout for authenticated users to enable client-side routing
      'spa'
    elsif params[:controller] == 'public_bookings'
      'tymlink'
    elsif params[:controller] == 'public_meetings'
      'meeting'
    else
      'welcome'
    end
  end

  def configure_permitted_parameters
    # Existing permit for user update
    devise_parameter_sanitizer.permit(:user_update, keys: [:password, :password_confirmation, :current_password])
    # Explicitly permit params for the reset password action
    devise_parameter_sanitizer.permit(:reset_password, keys: [:reset_password_token, :password, :password_confirmation])
  end

  def require_login
    if devise_password_reset_flow?
      #Skipping require_login for Devise password reset
      return
    end
    
    if public_spa_page?
      # Skipping require_login for public SPA pages (email confirmation, etc.)
      return
    end

    # Try JWT authentication first (silent - doesn't render on failure)
    authenticate_user_from_jwt_silent

    if @current_jwt_user
      # JWT authentication successful
      Rails.logger.info "Rails controller authenticated via JWT for user: #{@current_jwt_user.id}"
      return
    end

    # Try JWT session cookie authentication (for page refreshes)
    if authenticate_from_session_cookie
      Rails.logger.info "Rails controller authenticated via JWT session cookie for user: #{@current_jwt_user.id}"
      return
    end

    # No valid JWT provided - authentication failed
    Rails.logger.info "Rails controller authentication failed: No valid JWT provided"
    flash[:error] = t("flash.registration_needed", default: "Registration needed.")
    
    # API endpoints ALWAYS return JSON, never redirect
    if request.path.start_with?('/api/')
      render json: { error: 'JWT authentication required' }, status: :unauthorized
    else
      # Handle different request formats for non-API requests
      respond_to do |format|
        format.html { redirect_to spa_login_override_path(locale: I18n.locale) }
        format.json { render json: { error: 'JWT authentication required' }, status: :unauthorized }
      end
    end
  end

  def devise_password_reset_flow?
    params[:controller] == 'devise/passwords' && %w[edit update].include?(params[:action])
  end
  
  def public_spa_page?
    # Allow public access to specific SPA pages that handle authentication flows
    return false unless params[:controller] == 'spa' && params[:action] == 'index'
    
    public_paths = [
      # Email confirmation page
      "/#{params[:locale]}/confirm-email",
      # Password reset page  
      "/#{params[:locale]}/reset-password"
    ]
    
    public_paths.include?(request.path)
  end

  # CHUNK 34: Silent JWT authentication that doesn't render on failure
  # Based on the working pattern from API controllers
  def authenticate_user_from_jwt_silent
    token = extract_jwt_from_header
    return unless token
    
    payload = decode_and_validate_token(token)
    return unless payload
    
    user = find_user_from_payload(payload)
    return unless user
    
    # Check if token has been revoked
    return if token_revoked?(payload, user)
    
    @current_jwt_user = user
    
    # Extract and set tenant (company) context from JWT
    unless set_tenant_from_jwt(payload, user)
      # Invalid tenant context - clear the JWT user
      @current_jwt_user = nil
      Rails.logger.warn "Rails controller JWT authentication failed silently: Invalid tenant context"
      return
    end
    
    Rails.logger.info "Rails controller JWT authentication successful for user: #{user.id}"
    AuthHealthCheck.log_auth_event('jwt_authentication', success: true)
  rescue => e
    # Enhanced error logging with partial backtrace for debugging
    backtrace_snippet = e.backtrace&.first(3)&.join("\n  ")
    Rails.logger.error "Rails controller JWT authentication error: #{e.class.name} - #{e.message}\n  Backtrace:\n  #{backtrace_snippet}"
    nil
  end
  
  def user_not_authorized(exception)
    policy_name = exception.policy.to_s.underscore

    flash[:alert] = t "#{policy_name}.#{exception.rule}", scope: "action_policy", default: "Nemáte oprávnění."
    redirect_to root_path
  end

  def set_current_tenant
    # JWT tenant context has already been set in authenticate_user_from_jwt_silent if JWT auth was used
    if @current_jwt_user && ActsAsTenant.current_tenant
      # JWT authentication already set the tenant context, use it
      Rails.logger.debug "Using JWT tenant context: company_id=#{ActsAsTenant.current_tenant.id}"
      return
    end
    
    # No valid JWT tenant context found
    ActsAsTenant.current_tenant = nil
    Rails.logger.debug "No JWT tenant context available"
  end

  # JWT-only mode: Tenant context is set automatically from JWT payload
  # Manual tenant setting is no longer needed

  def after_accept_path_for(resource)
    puts "\n ---------- after_accept_path_for ----- \n\n"
    # JWT-only mode: Tenant context will be set when user logs in via JWT
    # The JWT will contain the correct company_id from user's primary role
    company_connections_path
  end
  
  # Commented out - JWT-only mode uses API endpoints for authentication
  # def after_sign_in_path_for(resource)
  #   puts "\n ---------- after_sign_in_path_for ----- \n\n"
  #   set_tenant_id_for_resource(resource)
  #   super(resource)
  # end

  # JWT-only mode: Session-based tenant management methods removed
  # Tenant context now comes exclusively from JWT payload

  # Commented out - JWT-only mode gets tenant context from JWT payload
  # def check_and_refresh_tenant_session
  #   # Skip session-based tenant management when using JWT authentication
  #   return if @current_jwt_user.present?
  #   
  #   if user_signed_in? && session[:tenant_id].nil? && current_user.company_user_roles.exists?
  #     # User is logged in but lost tenant session - restore it
  #     set_tenant_id_for_resource(current_user)
  #     set_current_tenant
  #   end
  # end

  # Only uncomment if you want session expiration separate from Devise timeout
  # def check_session_expiration
  #   if session[:tenant_id] && session[:last_activity_time] && Time.now - session[:last_activity_time] > 500.minutes.to_i
  #     reset_tenant_session
  #     redirect_to login_path, alert: "Your session has expired."
  #   else
  #     session[:last_activity_time] = Time.now
  #   end
  # end

  def http_basic_authenticate
    #authenticate_or_request_with_http_basic("Restricted Area") do |username, password|
      #username == ENV["HTTP_USERNAME"] && password == ENV["HTTP_PASSWORD"]
      #username == "tester" && password == "nasacesta"
    #end
  end

  # def skip_login_for_devise?
  #   # You can customize this based on the controller or actions you want to skip
  #   devise_controllers = ['devise/sessions', 'devise/registrations']
  #   devise_controllers.include?(params[:controller])
  # end

  def record_not_found(error)
    render json: { success: false, error: error.message }, status: :not_found
  end

  def set_locale
    
    # I18n.locale = params[:locale] || cookies[:locale] || I18n.default_locale
    
    # New logic for locale setting
     
    # Locale from URL path param
    locale_from_path = params[:locale]
    if locale_from_path.present? && I18n.available_locales.map(&:to_s).include?(locale_from_path)
      I18n.locale = locale_from_path
      # Update cookie if path locale is different and valid, ensuring it's kept in sync
      if cookies[:locale] != locale_from_path
        cookies[:locale] = { value: locale_from_path, expires: 1.year.from_now, same_site: :lax, path: '/' }
      end
      return
    end

    # Locale from cookie
    locale_from_cookie = cookies[:locale]
    if locale_from_cookie.present? && I18n.available_locales.map(&:to_s).include?(locale_from_cookie)
      I18n.locale = locale_from_cookie
      return
    end

    # 3. Default locale (fallback)
    I18n.locale = I18n.default_locale
  end

  # Automatically add locale to Rails-generated URLs
  def default_url_options(options = {})
    # Always include the locale in the path, consistent with path-based locale strategy.
    # This ensures that links generated by url_for, path helpers, etc., include the current locale.
    { locale: I18n.locale }.merge(options)
  end

end
