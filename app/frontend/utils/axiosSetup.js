// DEBUG: Module evaluation tracking
console.log('[DEBUG] axiosSetup.js: Module evaluation started at', new Date().toISOString());

import axios from 'axios';
import { sendFlashMessage } from './flashMessage';
import store from '../store';

// DEBUG: Assign unique ID to this axios instance
axios._axiosId = Math.random();
console.log('[DEBUG] axiosSetup.js: axios instance ID:', axios._axiosId);

// JWT-only authentication mode - no CSRF tokens or session cookies needed
// All authentication is handled via JWT tokens in Authorization headers

/**
 * JWT Authorization Header Request Interceptor
 * 
 * Automatically adds JWT tokens to Authorization headers when available.
 * This provides centralized header management and ensures consistent token usage
 * across all API requests during the dual authentication period.
 * 
 * Key Features:
 * - Automatic JWT header injection when token is available
 * - CSRF token compatibility for session-based requests
 * - Feature flag awareness for JWT enablement
 * - Graceful degradation when JWT storage fails
 */
axios.interceptors.request.use(
  (config) => {
    try {
      // Get JWT token from Vuex store if available
      const token = store.getters['userStore/jwtToken'];
      
      if (token) {
        // Add JWT Authorization header
        config.headers.Authorization = `Bearer ${token}`;
        
        // TODO: This interceptor will be enhanced with automatic token refresh logic
        // TODO: (Future Enhancement) Consider adding request ID for debugging dual auth scenarios
      }
      
      // NOTE: JWT-only authentication mode - no session dependencies
      
    } catch (error) {
      // Store access failed - log warning but don't fail the request
      console.warn('Failed to access JWT token for request:', error);
      // Request continues without JWT authorization - may fall back to session auth
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * JWT Refresh Token Response Interceptor
 * 
 * Handles automatic token refresh when JWT expires (401 errors).
 * This interceptor provides seamless user experience by automatically refreshing
 * expired tokens and retrying the original request.
 * 
 * Key Features:
 * - Detects JWT expiration via 401 errors
 * - Prevents multiple concurrent refresh attempts
 * - Retries original request with new token
 * - Graceful fallback and user logout on refresh failure
 */

// Track ongoing refresh attempts to prevent concurrent requests
let isRefreshing = false;
let failedQueue = [];

// Process queued requests after token refresh
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  
  failedQueue = [];
};

// Helper function for consistent authentication failure handling
const handleAuthFailureAndRedirect = (message = 'Vaše relace vypršela. Přihlaste se prosím znovu.', messageType = 'warning') => {
  // Clear authentication state from Vuex store before redirecting
  if (store) {
    try {
      console.log('[DEBUG] handleAuthFailureAndRedirect: Dispatching logout');
      store.dispatch('userStore/logout');
    } catch (error) {
      console.warn('Failed to dispatch logout action:', error);
    }
  }
  
  // Only redirect if we're not already on login page to prevent loops
  const currentPath = window.location.pathname;
  const isLoginPage = currentPath.includes('/sign_in') || currentPath.includes('/login');
  
  if (!isLoginPage) {
    sendFlashMessage(message, messageType);
    if (window.$router) {
      window.$router.push({ name: 'login' });
    } else {
      // CRITICAL FIX: Don't use any setTimeout or delayed navigation
      // This causes navigation loops. Instead, just log and let Vue router handle it naturally
      console.log('[DEBUG] Router not ready, skipping redirect to prevent navigation loops');
      // Do NOT navigate - let the Vue router navigation guard handle it naturally
      // when the user tries to access a protected route
    }
  }
  // If already on login, just clear auth state silently - no redirect needed!
};

axios.interceptors.response.use(
  response => {
    console.log('[DEBUG] Response interceptor: SUCCESS path triggered for', response.config?.url);
    if (response.data && response.data.message) {
      const messageText = response.data.message;
      const messageType = response.data.messageType || (response.data.success === false ? 'error' : 'success');

      sendFlashMessage(messageText, messageType);

      // const { text, type } = response.data.message;
      // if (text) {
      //   sendFlashMessage(text, type || 'info');
      // }
    }
    return response;
  },
  async error => {
    console.log('[DEBUG] Response interceptor: ERROR path triggered', {
      status: error.response?.status,
      url: error.config?.url,
      errorMessage: error.message
    });
    const originalRequest = error.config;

    // Handle 401 Unauthorized - potential JWT expiration
    if (error.response && error.response.status === 401) {
      
      // Check if this is a JWT-authenticated request that failed
      // and we have a JWT token (indicating potential expiration)
      const hasJwtToken = () => {
        console.log('[DEBUG] hasJwtToken: Checking store availability');
        try {
          const token = store.getters['userStore/jwtToken'];
          const authState = store.getters['userStore/isAuthenticated'];
          console.log('[DEBUG] hasJwtToken: Token exists?', !!token, 'isAuthenticated:', authState);
          if (!token) {
            console.log('[DEBUG] hasJwtToken: No token in store - checking why');
            console.log('[DEBUG] hasJwtToken: Store state:', {
              userStore: store.state.userStore || 'undefined',
              hasUserStore: !!store.state.userStore
            });
          }
          return !!token;
        } catch (error) {
          console.log('[DEBUG] hasJwtToken: Error accessing store:', error);
          return false;
        }
      };

      // Skip refresh for certain requests to avoid infinite loops
      if (originalRequest._skipRefreshInterceptor || 
          originalRequest.url?.includes('/auth/refresh_token') ||
          originalRequest.url?.includes('/auth/jwt_login') ||
          originalRequest.url?.includes('/auth/jwt_logout')) {
        
        // For auth-related endpoints that fail with 401, don't retry
        // CRITICAL FIX: For restore_session specifically, NEVER redirect
        // This is initial auth check during app startup and should not cause navigation
        if (originalRequest.url?.includes('/auth/restore_session')) {
          console.log('[DEBUG] restore_session failed, skipping redirect to prevent navigation loops');
          // Just clear auth state, Vue router navigation guard will handle redirect when needed
          if (store) {
            try {
              store.dispatch('userStore/logout');
            } catch (error) {
              console.warn('Failed to dispatch logout during restore_session failure:', error);
            }
          }
        } else {
          handleAuthFailureAndRedirect();
        }
        return Promise.reject(error);
      }

      // If we have a JWT token and this request failed with 401, 
      // it's likely due to token expiration - attempt refresh
      if (hasJwtToken() && !originalRequest._retry) {
        
        // Mark this request as retried to prevent infinite loops
        originalRequest._retry = true;

        if (isRefreshing) {
          // Another request is already refreshing the token
          // Queue this request to be retried after refresh completes
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(token => {
            // Retry the original request with the new token (interceptor will add it automatically)
            return axios.request(originalRequest);
          }).catch(err => {
            return Promise.reject(err);
          });
        }

        isRefreshing = true;

        try {
          // Dynamically import AuthService to avoid circular dependency
          const { default: AuthService } = await import('../services/authService');
          
          console.log('JWT token expired, attempting refresh...');
          await AuthService.refreshToken();
          
          console.log('Token refresh successful, retrying original request');
          processQueue(null, true);
          
          // Retry the original request with the new token
          return axios.request(originalRequest);
          
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError.message);
          processQueue(refreshError, null);
          
          // If refresh fails, redirect to login
          handleAuthFailureAndRedirect();
          
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      } else {
        // For non-JWT requests or other 401 scenarios (no token or already retried)
        handleAuthFailureAndRedirect();
        return Promise.reject(error);
      }
    }
    
    if (error.response && error.response.data) {
      if (error.response.data.message) {
        const messageType = error.response.data.messageType || 'error';
        sendFlashMessage(error.response.data.message, messageType);

        // const { text, type } = error.response.data.message;
        // if (text) {
        //   sendFlashMessage(text, type || 'error');
        // }

      } else if (error.response.data.errors) {
        const errorMessage = Array.isArray(error.response.data.errors) 
          ? error.response.data.errors.join(', ') 
          : Object.values(error.response.data.errors).flat().join(', ');
        
        sendFlashMessage(errorMessage, 'error');
      }
    } else {
      sendFlashMessage('Došlo k chybě při komunikaci se serverem.', 'error');
    }
    return Promise.reject(error);
  }
);

// DEBUG: Module evaluation complete
console.log('[DEBUG] axiosSetup.js: Module evaluation completed, interceptors:', {
  request: axios.interceptors.request.handlers.length,
  response: axios.interceptors.response.handlers.length
});

export default axios;