import '../css/main.css';
import '../css/application.css';
import '../utils/axiosSetup'; 
import axios from 'axios';


// DEBUG: Check axios instance after import
console.log('[DEBUG] application.js: axios instance ID:', axios._axiosId);
console.log('[DEBUG] application.js: axios interceptors:', {
  request: axios.interceptors?.request?.handlers?.length,
  response: axios.interceptors?.response?.handlers?.length
});

// Development-only console forwarding - tree-shaken in production
if (import.meta.env.DEV) {
  import('../dev/console-forwarder');
}
import { createApp } from 'vue';  
import store from '../store';
import i18n, { loadLocaleMessages, getStartingLocale } from '../i18n';
import router from '../router';
import App from '../App.vue';
import AuthService from '../services/authService';

// Import components to be registered globally
import EventForm from '../components/events/EventForm.vue';
import WorkForm from '../components/works/WorkForm.vue';
import ServiceContractForm from '../components/works/ServiceContractForm.vue';
import WorkShow from '../components/works/WorkShow.vue';
import MeetingForm from '../components/meetings/MeetingForm.vue';
import CookieConsent from '../components/CookieConsent.vue';

// JWT-only authentication mode - no CSRF tokens needed
// All authentication is handled via JWT tokens in Authorization headers

// Paths that do not require an authenticated user
// The app will skip the initial authentication check on these routes
const AUTH_BYPASS_PATHS = [
  '/login',
  '/register',           // Primary registration path
  '/users/sign_up',      // Legacy Devise registration path  
  '/users/password',     // Devise "forgot password" form path
  'confirm-email',       // Email confirmation (TYM-37 fix) - no leading slash for locale paths
  'forgot-password',     // Primary "forgot password" path - no leading slash for locale paths
  'reset-password',      // Password reset form - no leading slash for locale paths
  'auth/accept-invitation'  // Invitation acceptance - no leading slash for locale paths
];

// Set default headers for JSON requests
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.patch['Content-Type'] = 'application/json';

// Initialize app data
async function initializeApp() {
  try {
    // Load locale messages
    const locale = getStartingLocale();
    await loadLocaleMessages(i18n, locale);
    
    // Check if we're on an auth page - skip authenticated data fetching
    // JWT-only mode: Updated to use consistent auth paths
    console.log('[DEBUG] Checking auth bypass for path:', window.location.pathname);
    console.log('[DEBUG] AUTH_BYPASS_PATHS:', AUTH_BYPASS_PATHS);
    const isAuthPage = AUTH_BYPASS_PATHS.some(path => {
      const matches = window.location.pathname.includes(path);
      console.log('[DEBUG] Path check:', path, 'matches:', matches);
      return matches;
    });
    
    if (isAuthPage) {
      console.log('[DEBUG] Auth page detected - skipping authenticated data fetching');
      return { current_plan: 'free', available_features: [] };
    }
    
    console.log('[DEBUG] NOT an auth page - proceeding with authentication check');
    // Ensure user is authenticated before proceeding
    const isAuthenticated = await AuthService.ensureAuthenticated();
    if (!isAuthenticated) {
      // If not authenticated, let the interceptor handle redirect.
      // Return default values to prevent app from crashing.
      return { current_plan: 'free', available_features: [] };
    }
    
    // Fetch initial data for authenticated pages
    const subscriptionResponse = await axios.get('/api/v1/subscription_status');
    window.appSubscription = subscriptionResponse.data;
    
    // User data is already fetched by ensureAuthenticated
    
    return subscriptionResponse.data;
  } catch (error) {
    console.error('Error initializing app:', error);
    // If not authenticated, return default values
    return { current_plan: 'free', available_features: [] };
  }
}

// Smart service worker registration that respects development settings
async function smartRegisterServiceWorker() {
  // Development mode controls
  if (import.meta.env.DEV) {
    // Check localStorage override for manual SW control in development
    const enablePWAInDev = localStorage.getItem('attendify_enable_pwa_dev');
    
    if (enablePWAInDev === 'false') {
      console.log('[PWA] Service worker disabled by localStorage (attendify_enable_pwa_dev=false)');
      console.log('[PWA] To enable: localStorage.setItem("attendify_enable_pwa_dev", "true")');
      return null;
    }
    
    // Check if DevTools has unregistered service workers
    if ('serviceWorker' in navigator) {
      try {
        const registrations = await navigator.serviceWorker.getRegistrations();
        const hasActiveRegistration = registrations.some(reg => 
          reg.active && reg.scope === window.location.origin + '/'
        );
        
        // If no active registration exists and we haven't explicitly enabled PWA in dev,
        // respect the DevTools unregister state
        if (!hasActiveRegistration && enablePWAInDev !== 'true') {
          console.log('[PWA] No active service worker found - respecting DevTools state');
          console.log('[PWA] To force enable: localStorage.setItem("attendify_enable_pwa_dev", "true")');
          return null;
        }
        
        console.log('[PWA] Development mode - service worker registration proceeding');
        console.log('[PWA] To disable: localStorage.setItem("attendify_enable_pwa_dev", "false")');
      } catch (error) {
        console.warn('[PWA] Failed to check service worker registrations:', error);
      }
    }
  }
  
  return await registerServiceWorker();
}

// Register service worker for PWA functionality
async function registerServiceWorker() {
  if ('serviceWorker' in navigator) {
    try {
      console.log('[PWA] Registering service worker...');
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      
      console.log('[PWA] Service worker registered successfully:', registration);
      
      // Handle controller changes (when SW actually takes control)
      // Guard against multiple listener attachments in HMR/dev environments
      if (!window.pwaControllerChangeListenerAttached) {
        let refreshing = false;
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          // TYM-83 FIX: Only reload in production when controller changes
          // In development, service worker updates are handled differently
          if (import.meta.env.DEV) {
            console.log('[PWA] Controller changed in dev mode - skipping reload');
            return;
          }
          
          if (refreshing) return;
          console.log('[PWA] Controller changed, reloading page...');
          refreshing = true;
          window.location.reload();
        });
        window.pwaControllerChangeListenerAttached = true;
      }
      
      // Handle updates with user control - enhanced for development
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('[PWA] New version available');
              
              // Enhanced development mode handling
              if (import.meta.env.DEV) {
                // In development, provide more control and prevent loops
                const autoUpdate = localStorage.getItem('attendify_pwa_auto_update_dev');
                
                if (autoUpdate === 'true') {
                  console.log('[PWA] Development mode - auto-updating service worker');
                  newWorker.postMessage({ type: 'SKIP_WAITING' });
                } else {
                  console.log('[PWA] Development mode - service worker update available');
                  console.log('[PWA] To auto-update: localStorage.setItem("attendify_pwa_auto_update_dev", "true")');
                  console.log('[PWA] To manually update: call location.reload() or refresh browser');
                  
                  // Don't show update prompt in dev mode to prevent confusion
                  // Developer can control updates manually
                }
              } else {
                // Production mode - show update notification to user
                showUpdatePrompt(newWorker);
              }
            }
          });
        }
      });
      
      return registration;
      
    } catch (error) {
      console.error('[PWA] Service worker registration failed:', error);
      return null;
    }
  } else {
    console.log('[PWA] Service workers not supported');
    return null;
  }
}

// Show update prompt to user
function showUpdatePrompt(newWorker) {
  // TYM-83 FIX: Only show update prompt in production, not in development
  // In development, updates are handled by HMR and DevTools
  if (import.meta.env.DEV) {
    console.log('[PWA] Development mode - skipping update prompt');
    return;
  }
  
  // Simple confirmation dialog - in production this could be a nicer UI
  const updateAvailable = confirm(
    'A new version of the app is available. Would you like to update now? The page will reload.'
  );
  
  if (updateAvailable) {
    console.log('[PWA] User confirmed update, triggering skipWaiting');
    newWorker.postMessage({ type: 'SKIP_WAITING' });
  } else {
    console.log('[PWA] User declined update, will update on next page load');
  }
}

// Create and mount the app
document.addEventListener('DOMContentLoaded', async () => {
  // Smart service worker registration for development
  // Allow localhost, local IPs, and HTTPS
  const isLocalDev = window.location.hostname === 'localhost' || 
                     window.location.hostname === '************' ||
                     window.location.hostname.startsWith('192.168.');
  const isSecure = window.location.protocol === 'https:';
  
  if (isLocalDev || isSecure) {
    await smartRegisterServiceWorker();
  } else {
    console.log('[PWA] Service worker registration skipped (requires HTTPS or local development)');
  }
  
  // Check if we're on a SPA page
  const appElement = document.getElementById('app');
  
  if (appElement) {
    // SPA mode - mount the full app
    const subscriptionData = await initializeApp();
    
    // Create the Vue app
    const app = createApp(App);
    
    // Register global components
    app.component('EventForm', EventForm);
    app.component('WorkForm', WorkForm);
    app.component('ServiceContractForm', ServiceContractForm);
    app.component('MeetingForm', MeetingForm);
    app.component('CookieConsent', CookieConsent);
    // Feedback components
    app.component('FeedbackForm', (await import('../components/feedback/FeedbackForm.vue')).default);
    app.component('FeedbackButton', (await import('../components/feedback/FeedbackButton.vue')).default);

    // Use plugins
    app.use(store);
    app.use(i18n);
    app.use(router);
    
    // Provide subscription data globally
    app.provide('subscription', subscriptionData);
    
    // Expose router, store, and i18n globally for axios interceptor and debugging
    window.$router = router;
    window.$store = store;
    window.$i18n = i18n;
    
    // Mount the app
    app.mount('#app');
  } else {
    // Legacy mode - mount individual components
    await mountLegacyComponents();
  }
  
  // Handle analytics if needed
  const flashMessages = JSON.parse(document.getElementById('flash-messages')?.dataset.messages || '{}');
  if (flashMessages.analytics_event === "registration_complete") {
    if (typeof gtag !== 'undefined') {
      gtag('event', 'conversion', {
        'event_category': 'registration',
        'event_label': 'user_signup',
        'send_to': 'G-HWE817PRNM'
      });
      gtag('event', 'user_registration');
    }
  }
});

// Legacy component mounting for non-SPA pages
async function mountLegacyComponents() {
  // Import components for legacy mode
  const { createApp: createLegacyApp } = await import('vue');
  const FlashMessages = (await import('../components/FlashMessages.vue')).default;
  const Sidebar = (await import('../components/Sidebar.vue')).default;
  const Topbar = (await import('../components/Topbar.vue')).default;
  const Mainbox = (await import('../components/Mainbox.vue')).default;
  const DailyLogsApp = (await import('../components/dailylogs/DailyLogsIndex.vue')).default;
  const EventList = (await import('../components/events/EventList.vue')).default;
  const CompanyIndex = (await import('../components/companies/CompanyIndex.vue')).default;
  const MonthlyReport = (await import('../components/MonthlyReport.vue')).default;
  const CompanyConnections = (await import('../components/CompanyConnections.vue')).default;
  const ContractsList = (await import('../components/contracts/ContractsList.vue')).default;
  const WorksIndex = (await import('../components/works/WorksIndex.vue')).default;
  const BookingsIndex = (await import('../components/bookings/BookingsIndex.vue')).default;
  const UserSettingsForm = (await import('../components/user_profile/UserSettingsForm.vue')).default;
  const ActivityDashboard = (await import('../components/reports/ActivityDashboard.vue')).default;
  const CentralModal = (await import('../components/shared/CentralModal.vue')).default;
  const Holidays = (await import('../components/Holidays.vue')).default;

  // Initialize app data
  try {
    // Check if we're on an auth page - skip authenticated data fetching
    console.log('[DEBUG] Legacy components: Checking auth bypass for path:', window.location.pathname);
    const isAuthPage = AUTH_BYPASS_PATHS.some(path => {
      const matches = window.location.pathname.includes(path);
      console.log('[DEBUG] Legacy path check:', path, 'matches:', matches);
      return matches;
    });
    
    if (!isAuthPage) {
      // Ensure user is authenticated before proceeding
      const isAuthenticated = await AuthService.ensureAuthenticated();
      if (isAuthenticated) {
        const subscriptionResponse = await axios.get('/api/v1/subscription_status');
        window.appSubscription = subscriptionResponse.data;
        // User data is already fetched by ensureAuthenticated
      }
    }
  } catch (error) {
    console.error('Error initializing app data:', error);
  }

  // Load translations
  const locale = getStartingLocale();
  await loadLocaleMessages(i18n, locale);

  // Create a mini router for legacy mode navigation
  const legacyRouter = {
    push: (path) => {
      window.location.href = path;
    }
  };

  // Mount individual components
  const mountComponent = (selector, Component, props = {}) => {
    const element = document.querySelector(selector);
    if (element) {
      const app = createLegacyApp(Component, props);
      app.use(store);
      app.use(i18n);
      // Make router available globally for components
      app.config.globalProperties.$router = legacyRouter;
      app.mount(element);
    }
  };

  // Mount flash messages
  mountComponent('#flash-messages', FlashMessages);

  // Mount cookie consent
  mountComponent('#cookie-consent', CookieConsent);

  // Mount sidebar
  const sidebarElement = document.getElementById('sidebar-app');
  if (sidebarElement) {
    const propsData = { ...sidebarElement.dataset };
    if (propsData.isWorking) {
      propsData.isWorking = propsData.isWorking === 'true';
    }
    mountComponent('#sidebar-app', Sidebar, propsData);
  }

  // Mount topbar
  const topbarElement = document.getElementById('topbar-app');
  if (topbarElement) {
    mountComponent('#topbar-app', Topbar, { ...topbarElement.dataset });
  }

  // Mount other components
  mountComponent('#holidays-app', Holidays);
  mountComponent('#daily-logs-index', DailyLogsApp);
  mountComponent('#event-list', EventList);
  mountComponent('#company-list', CompanyIndex);
  mountComponent('#works-index', WorksIndex);
  mountComponent('#bookings-index', BookingsIndex);
  mountComponent('#user-settings-form', UserSettingsForm);
  mountComponent('#mainbox', Mainbox, { subscription: window.appSubscription });
  mountComponent('#monthly-report', MonthlyReport);
  mountComponent('#contracts-list', ContractsList);
  mountComponent('#activity-dashboard', ActivityDashboard);

  // Mount company connections
  const companyConnectionsElement = document.getElementById('company-connections');
  if (companyConnectionsElement) {
    mountComponent('#company-connections', CompanyConnections, { 
      embedded: companyConnectionsElement.dataset.embedded 
    });
  }

  // Mount central modal with global components
  const centralModalElement = document.getElementById('central-modal-app');
  if (centralModalElement) {
    const modalApp = createLegacyApp(CentralModal);
    modalApp.component('EventForm', EventForm);
    modalApp.component('WorkForm', WorkForm);
    modalApp.component('ServiceContractForm', ServiceContractForm);
    modalApp.component('WorkShow', WorkShow);
    modalApp.component('MeetingForm', MeetingForm);
    // Feedback form for CentralModal in legacy mode
    modalApp.component('FeedbackForm', (await import('../components/feedback/FeedbackForm.vue')).default);
    modalApp.use(store);
    modalApp.use(i18n);
    modalApp.mount(centralModalElement);
  }
}

// Development utilities for service worker control
// Expose global utilities for easier debugging
if (import.meta.env.DEV) {
  window.AttendifyPWAUtils = {
    // Enable/disable PWA in development
    enablePWA: () => {
      localStorage.setItem('attendify_enable_pwa_dev', 'true');
      console.log('[PWA Utils] PWA enabled in dev mode. Refresh page to register service worker.');
    },
    
    disablePWA: () => {
      localStorage.setItem('attendify_enable_pwa_dev', 'false');
      console.log('[PWA Utils] PWA disabled in dev mode. Use unregisterAll() to remove existing registrations.');
    },
    
    // Enable/disable auto-updates
    enableAutoUpdate: () => {
      localStorage.setItem('attendify_pwa_auto_update_dev', 'true');
      console.log('[PWA Utils] Auto-update enabled. Service worker will update automatically on changes.');
    },
    
    disableAutoUpdate: () => {
      localStorage.setItem('attendify_pwa_auto_update_dev', 'false');
      console.log('[PWA Utils] Auto-update disabled. You control when to update service worker.');
    },
    
    // Manually unregister all service workers
    unregisterAll: async () => {
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (let registration of registrations) {
          await registration.unregister();
          console.log('[PWA Utils] Unregistered:', registration.scope);
        }
        console.log('[PWA Utils] All service workers unregistered.');
      }
    },
    
    // Show current status
    status: async () => {
      console.group('[PWA Utils] Current Status');
      console.log('PWA Enabled:', localStorage.getItem('attendify_enable_pwa_dev') !== 'false');
      console.log('Auto Update:', localStorage.getItem('attendify_pwa_auto_update_dev') === 'true');
      
      if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        console.log('Active Service Workers:', registrations.length);
        registrations.forEach((reg, i) => {
          console.log(`  ${i + 1}. Scope: ${reg.scope}, State: ${reg.active?.state || 'none'}`);
        });
      }
      console.groupEnd();
    },
    
    // Show help
    help: () => {
      console.group('[PWA Utils] Available Commands');
      console.log('AttendifyPWAUtils.enablePWA()        - Enable PWA in development');
      console.log('AttendifyPWAUtils.disablePWA()       - Disable PWA in development'); 
      console.log('AttendifyPWAUtils.enableAutoUpdate() - Enable automatic SW updates');
      console.log('AttendifyPWAUtils.disableAutoUpdate() - Disable automatic SW updates');
      console.log('AttendifyPWAUtils.unregisterAll()    - Unregister all service workers');
      console.log('AttendifyPWAUtils.status()           - Show current PWA status');
      console.log('AttendifyPWAUtils.help()             - Show this help');
      console.groupEnd();
    }
  };
  
  // Show initial help on page load
  console.log('[PWA] Development utilities loaded. Type AttendifyPWAUtils.help() for commands.');
}