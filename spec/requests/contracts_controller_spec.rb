require 'rails_helper'
require 'webmock/rspec'

RSpec.describe "ContractsControllers", type: :request do
  let!(:owner_role) { Role.find_or_create_by!(name: 'owner') }
  let!(:employee_role) { Role.find_or_create_by!(name: 'employee') }
  let(:company) { create(:company) }
  let!(:premium_plan) { create(:plan, :premium) }
  let!(:company_subscription) { create(:subscription, company: company, plan: premium_plan) }
  let(:owner) { create(:user, password: 'password123') }
  let(:employee) { create(:user, password: 'password123') }

  let!(:owner_company_role) { create(:company_user_role, user: owner, company: company, role: owner_role) }
  let!(:employee_company_role) { create(:company_user_role, user: employee, company: company, role: employee_role) }

  let!(:owner_contract) { create(:contract, company: company, user: owner, email: owner.email, first_name: 'Owner', last_name: 'User', status: :active) }
  let!(:employee_contract) { create(:contract, company: company, user: employee, email: employee.email, first_name: 'Employee', last_name: 'User', status: :active) }
  let!(:other_active_contract) { create(:contract, company: company, first_name: 'Other', last_name: 'Active', status: :active) }
  let!(:suspended_contract) { create(:contract, company: company, first_name: 'Suspended', last_name: 'User', status: :suspended) }

  # Use the around block for database scoping
  around do |example|
    ActsAsTenant.with_tenant(company) do
      example.run
    end
  end

  # Ensure @company is set in controller instance before actions run
  before do
    allow_any_instance_of(ContractsController).to receive(:set_tenant_company) do |controller|
      controller.instance_variable_set(:@company, company)
    end
    # Stub external API call
    stub_request(:get, /openholidaysapi\.org/).
      to_return(status: 200, body: "[]", headers: {'Content-Type' => 'application/json'})
  end

  # Helper methods for JWT authentication
  def jwt_login(user)
    post '/api/v1/auth/jwt_login', params: {
      email: user.email,
      password: 'password123'
    }
    
    expect(response).to have_http_status(:ok)
    token_data = JSON.parse(response.body)
    token_data['access_token']
  end

  def jwt_headers(user)
    token = jwt_login(user)
    { 'Authorization' => "Bearer #{token}", 'Accept' => 'application/json' }
  end

  # NO complex Authorization mocks - rely on actual policies and Devise/ActionPolicy handling

  describe "GET /contracts/fetch" do
    context "when user is an owner (has manage_contract? permission)" do
      it "returns http success and full contract data for JSON request" do
        get fetch_contracts_path, headers: jwt_headers(owner)
        # Let's expect success for now, assuming policy passes
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['contracts']).to be_an(Array)
        expect(json_response['contracts'].first).to include('days_worked') # Verify summary data
        expect(json_response['contracts'].size).to eq(company.contracts.count)
      end
    end

    context "when user is an employee (lacks manage_contract? permission)" do
      # Default ActionPolicy/Devise behavior for unauthorized JSON is likely redirect here too
      it "redirects for JSON request" do 
        get fetch_contracts_path, headers: jwt_headers(employee)
        expect(response).to redirect_to(root_path) # Adjust if redirects elsewhere
      end

      it "redirects for HTML request" do
        get fetch_contracts_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context "when user is not authenticated" do
      # Devise should redirect to login when accessing authenticated actions
      it "redirects to login when accessing a Devise protected path" do
        get '/cs/users/edit' # This path exists but controller action requires auth
        expect(response).to redirect_to('/cs/users/sign_in')
      end
    end
  end

  describe "GET /contracts/colleagues" do
    context "when user is an owner" do
      it "returns http success and basic active colleague data (excluding self)" do
        # Policy view_colleagues? should allow based on CompanyUserRole existence
        get colleagues_contracts_path, headers: jwt_headers(owner)
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to be_an(Array)
        expect(json_response.size).to eq(2) # employee_contract + other_active_contract
        expect(json_response.first).to include('id', 'first_name', 'last_name', 'email', 'job_title')
        expect(json_response.first).not_to include('days_worked', 'status')
        expect(json_response.map { |c| c['id'] }).not_to include(owner_contract.id)
        expect(json_response.map { |c| c['id'] }).not_to include(suspended_contract.id)
        expect(json_response.map { |c| c['id'] }).to include(employee_contract.id, other_active_contract.id)
      end
    end

    context "when user is an employee" do
      it "returns http success and basic active colleague data (excluding self)" do
        get colleagues_contracts_path, headers: jwt_headers(employee)
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to be_an(Array)
        expect(json_response.size).to eq(2) # owner_contract + other_active_contract
        expect(json_response.first).to include('id', 'first_name', 'last_name', 'email', 'job_title')
        expect(json_response.first).not_to include('days_worked', 'status')
        expect(json_response.map { |c| c['id'] }).not_to include(employee_contract.id)
        expect(json_response.map { |c| c['id'] }).not_to include(suspended_contract.id)
        expect(json_response.map { |c| c['id'] }).to include(owner_contract.id, other_active_contract.id)
      end
    end

    context "when user is not authenticated" do
      # Devise should redirect to login when accessing authenticated actions
      it "redirects to login when accessing a Devise protected path" do
        # Test the general authentication requirement using a Devise example
        get '/cs/users/edit'
        expect(response).to redirect_to('/cs/users/sign_in')
      end
    end
  end

  describe "POST /contracts/:id/update_role" do
    let!(:admin_role) { Role.find_or_create_by!(name: 'admin') }
    let!(:second_owner) { create(:user, password: 'password123') }
    let!(:second_owner_company_role) { create(:company_user_role, user: second_owner, company: company, role: owner_role) }
    let!(:second_owner_contract) { create(:contract, company: company, user: second_owner, email: second_owner.email, first_name: 'Second', last_name: 'Owner', status: :active) }

    context "when user is an owner" do
      context "SECURITY: owner trying to change own role" do
        it "prevents owner from changing own role to employee" do
          post update_role_contract_path(owner_contract), 
               params: { role_name: 'employee' },
               headers: jwt_headers(owner)
          
          expect(response).to have_http_status(:forbidden)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['message']).to eq('Nemůžete změnit svou vlastní roli vlastníka.')
          
          # Verify role didn't change
          owner.reload
          expect(owner.has_role?('owner', company)).to be true
        end

        it "prevents owner from changing own role to admin" do
          post update_role_contract_path(owner_contract), 
               params: { role_name: 'admin' },
               headers: jwt_headers(owner)
          
          expect(response).to have_http_status(:forbidden)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['message']).to eq('Nemůžete změnit svou vlastní roli vlastníka.')
        end
      end

      context "SECURITY: preventing last owner removal" do
        it "prevents changing the last owner's role when only one owner exists" do
          # Remove the second owner to simulate single owner scenario
          second_owner_company_role.destroy
          
          # Now try to change the owner (should fail as it's the last owner)
          post update_role_contract_path(owner_contract),
               params: { role_name: 'employee' },
               headers: jwt_headers(owner)
          
          expect(response).to have_http_status(:forbidden)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['message']).to eq('Společnost musí mít alespoň jednoho vlastníka.')
        end

        it "allows changing owner role when multiple owners exist" do
          # This test shows the second owner can change the first owner's role
          post update_role_contract_path(owner_contract),
               params: { role_name: 'admin' },
               headers: jwt_headers(second_owner)
          
          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
          expect(json_response['message']).to eq('Role byla aktualizována.')
          
          # Verify role changed
          owner.reload
          expect(owner.has_role?('admin', company)).to be true
          expect(owner.has_role?('owner', company)).to be false
        end
      end

      context "normal role changes" do
        it "allows owner to change employee role to admin" do
          post update_role_contract_path(employee_contract),
               params: { role_name: 'admin' },
               headers: jwt_headers(owner)
          
          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
          expect(json_response['message']).to eq('Role byla aktualizována.')
          expect(json_response['role']).to eq('admin')
          
          # Verify role changed
          employee.reload
          expect(employee.has_role?('admin', company)).to be true
          expect(employee.has_role?('employee', company)).to be false
        end

        it "allows owner to change employee role to owner" do
          post update_role_contract_path(employee_contract),
               params: { role_name: 'owner' },
               headers: jwt_headers(owner)
          
          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
          expect(json_response['role']).to eq('owner')
          
          # Verify role changed
          employee.reload
          expect(employee.has_role?('owner', company)).to be true
        end
      end
    end

    context "when user is an employee" do
      it "redirects unauthorized user (employee cannot manage contracts)" do
        post update_role_contract_path(owner_contract),
             params: { role_name: 'admin' },
             headers: jwt_headers(employee)
        
        expect(response).to be_redirect
        expect(response.location).to include('/')
      end
    end

    context "edge cases" do
      it "handles non-existent role gracefully" do
        post update_role_contract_path(employee_contract),
             params: { role_name: 'nonexistent' },
             headers: jwt_headers(owner)
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
      end

      it "handles contract without connected user" do
        post update_role_contract_path(other_active_contract),
             params: { role_name: 'admin' },
             headers: jwt_headers(owner)
        
        expect(response).to have_http_status(:unprocessable_entity)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['message']).to eq('Uživatel není připojen k tomuto kontraktu.')
      end
    end

    context "free tier owner restrictions" do
      # Test with regular company (no subscription = free tier)
      it "prevents adding second owner on free tier" do
        # Create a free tier company with one owner
        free_tier_company = create(:company)
        free_tier_owner = create(:user, password: 'password123')
        free_tier_employee = create(:user, password: 'password123')
        
        ActsAsTenant.with_tenant(free_tier_company) do
          create(:company_user_role, user: free_tier_owner, company: free_tier_company, role: owner_role)
          create(:company_user_role, user: free_tier_employee, company: free_tier_company, role: employee_role)
          free_tier_owner_contract = create(:contract, company: free_tier_company, user: free_tier_owner, email: free_tier_owner.email, first_name: 'Free', last_name: 'Owner', status: :active)
          free_tier_employee_contract = create(:contract, company: free_tier_company, user: free_tier_employee, email: free_tier_employee.email, first_name: 'Free', last_name: 'Employee', status: :active)

          # Override tenant setup for this test
          allow_any_instance_of(ContractsController).to receive(:set_tenant_company) do |controller|
            controller.instance_variable_set(:@company, free_tier_company)
          end
          
          post update_role_contract_path(free_tier_employee_contract),
               params: { role_name: 'owner' },
               headers: jwt_headers(free_tier_owner)
          
          expect(response).to have_http_status(:forbidden)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['message']).to eq('Bezplatná verze podporuje pouze jednoho vlastníka. Pro přidání dalších vlastníků upgradujte na placenou verzi.')
          expect(json_response['upgrade_required']).to be true
          
          # Verify role didn't change
          free_tier_employee.reload
          expect(free_tier_employee.has_role?('employee', free_tier_company)).to be true
          expect(free_tier_employee.has_role?('owner', free_tier_company)).to be false
        end
      end

      it "allows first owner assignment on free tier" do
        free_tier_company_no_owner = create(:company)
        user_without_role = create(:user, password: 'password123')
        
        ActsAsTenant.with_tenant(free_tier_company_no_owner) do
          contract_without_role = create(:contract, company: free_tier_company_no_owner, user: user_without_role, email: user_without_role.email, first_name: 'No', last_name: 'Role', status: :active)
          
          # Override tenant setup for this test
          allow_any_instance_of(ContractsController).to receive(:set_tenant_company) do |controller|
            controller.instance_variable_set(:@company, free_tier_company_no_owner)
          end

          post update_role_contract_path(contract_without_role),
               params: { role_name: 'owner' },
               headers: jwt_headers(user_without_role)
          
          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
          expect(json_response['role']).to eq('owner')
          
          # Verify role changed
          user_without_role.reload
          expect(user_without_role.has_role?('owner', free_tier_company_no_owner)).to be true
        end
      end
    end
  end
end 